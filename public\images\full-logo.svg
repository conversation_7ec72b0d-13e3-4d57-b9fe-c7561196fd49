<svg width="300" height="221" viewBox="0 0 300 221" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_64_4)">
<g filter="url(#filter0_d_64_4)">
<path d="M120.641 119H97.5469L86 99L97.5469 79H120.641L132.188 99L120.641 119Z" fill="#133644"/>
<path d="M119.774 80.5L130.455 99L119.774 117.5H98.4131L87.7314 99L98.4131 80.5H119.774Z" stroke="#9AD3F1" stroke-width="3"/>
</g>
<g filter="url(#filter1_d_64_4)">
<path d="M189.641 80H166.547L155 60L166.547 40H189.641L201.188 60L189.641 80Z" fill="#133644"/>
<path d="M188.774 41.5L199.455 60L188.774 78.5H167.413L156.731 60L167.413 41.5H188.774Z" stroke="#9AD3F1" stroke-width="3"/>
</g>
<g filter="url(#filter2_d_64_4)">
<path d="M222.641 60H199.547L188 40L199.547 20H222.641L234.188 40L222.641 60Z" fill="#133644"/>
<path d="M221.774 21.5L232.455 40L221.774 58.5H200.413L189.731 40L200.413 21.5H221.774Z" stroke="#9AD3F1" stroke-width="3"/>
</g>
<g filter="url(#filter3_d_64_4)">
<path d="M255.641 40H232.547L221 20L232.547 0H255.641L267.188 20L255.641 40Z" fill="#133644"/>
<path d="M254.774 1.5L265.455 20L254.774 38.5H233.413L222.731 20L233.413 1.5H254.774Z" stroke="#9AD3F1" stroke-width="3"/>
</g>
<g filter="url(#filter4_d_64_4)">
<path d="M189.641 119H166.547L155 99L166.547 79H189.641L201.188 99L189.641 119Z" fill="#133644"/>
<path d="M188.774 80.5L199.455 99L188.774 117.5H167.413L156.731 99L167.413 80.5H188.774Z" stroke="#9AD3F1" stroke-width="3"/>
</g>
<g filter="url(#filter5_i_64_4)">
<path d="M222.641 99H199.547L188 79L199.547 59H222.641L234.188 79L222.641 99Z" fill="#9AD3F1"/>
</g>
<path d="M221.774 60.5L232.455 79L221.774 97.5H200.413L189.731 79L200.413 60.5H221.774Z" stroke="#133644" stroke-width="3"/>
<g filter="url(#filter6_d_64_4)">
<path d="M256.641 117H233.547L222 97L233.547 77H256.641L268.188 97L256.641 117Z" fill="#133644"/>
<path d="M255.774 78.5L266.455 97L255.774 115.5H234.413L223.731 97L234.413 78.5H255.774Z" stroke="#9AD3F1" stroke-width="3"/>
</g>
<g filter="url(#filter7_i_64_4)">
<path d="M290.641 99H267.547L256 79L267.547 59H290.641L302.188 79L290.641 99Z" fill="#9AD3F1"/>
</g>
<path d="M289.774 60.5L300.455 79L289.774 97.5H268.413L257.731 79L268.413 60.5H289.774Z" stroke="#133644" stroke-width="3"/>
<g filter="url(#filter8_d_64_4)">
<mask id="path-17-inside-1_64_4" fill="white">
<path d="M154 5C170.483 5 185.102 12.9764 194.21 25.2793L186.866 38H164.413L152.866 58H130.413L119.443 77H96.4131L84 98.5L96.4131 120H121.238L132.208 101H153.443L164.413 120H187.443L198.413 139H223.238L234.785 119H256.238L267.785 99H281.26C283.668 104.858 285 111.273 285 118C285 145.614 262.614 168 235 168C224.495 168 214.748 164.757 206.701 159.223C199.232 164.738 189.997 168 180 168C166.316 168 154.061 161.891 145.808 152.253C138.499 161.823 126.972 168 114 168C103.057 168 93.1419 163.604 85.9199 156.483C77.2601 163.674 66.1358 168 54 168C26.3858 168 4 145.614 4 118C4 90.3858 26.3858 68 54 68C54.3341 68 54.6674 68.0042 55 68.0107V68C55 45.9086 72.9086 28 95 28C100.36 28 105.473 29.0572 110.145 30.9697C118.643 15.492 135.096 5 154 5Z"/>
</mask>
<path d="M154 5C170.483 5 185.102 12.9764 194.21 25.2793L186.866 38H164.413L152.866 58H130.413L119.443 77H96.4131L84 98.5L96.4131 120H121.238L132.208 101H153.443L164.413 120H187.443L198.413 139H223.238L234.785 119H256.238L267.785 99H281.26C283.668 104.858 285 111.273 285 118C285 145.614 262.614 168 235 168C224.495 168 214.748 164.757 206.701 159.223C199.232 164.738 189.997 168 180 168C166.316 168 154.061 161.891 145.808 152.253C138.499 161.823 126.972 168 114 168C103.057 168 93.1419 163.604 85.9199 156.483C77.2601 163.674 66.1358 168 54 168C26.3858 168 4 145.614 4 118C4 90.3858 26.3858 68 54 68C54.3341 68 54.6674 68.0042 55 68.0107V68C55 45.9086 72.9086 28 95 28C100.36 28 105.473 29.0572 110.145 30.9697C118.643 15.492 135.096 5 154 5Z" fill="url(#paint0_linear_64_4)"/>
<path d="M194.21 25.2793L196.808 26.7792L197.792 25.0754L196.621 23.4943L194.21 25.2793ZM186.866 38V41H188.598L189.464 39.4999L186.866 38ZM164.413 38V35H162.681L161.815 36.5L164.413 38ZM152.866 58V61H154.598L155.464 59.5L152.866 58ZM130.413 58V55H128.681L127.815 56.5L130.413 58ZM119.443 77V80H121.175L122.041 78.5L119.443 77ZM96.4131 77V74H94.681L93.815 75.5L96.4131 77ZM84 98.5L81.4019 97L80.5359 98.5L81.4019 100L84 98.5ZM96.4131 120L93.815 121.5L94.681 123H96.4131V120ZM121.238 120V123H122.97L123.836 121.5L121.238 120ZM132.208 101V98H130.476L129.61 99.5L132.208 101ZM153.443 101L156.041 99.5L155.175 98H153.443V101ZM164.413 120L161.815 121.5L162.681 123H164.413V120ZM187.443 120L190.041 118.5L189.175 117H187.443V120ZM198.413 139L195.815 140.5L196.681 142H198.413V139ZM223.238 139V142H224.97L225.836 140.5L223.238 139ZM234.785 119V116H233.053L232.187 117.5L234.785 119ZM256.238 119V122H257.97L258.836 120.5L256.238 119ZM267.785 99V96H266.053L265.187 97.5L267.785 99ZM281.26 99L284.034 97.8592L283.27 96H281.26V99ZM206.701 159.223L208.401 156.751L206.639 155.539L204.919 156.809L206.701 159.223ZM145.808 152.253L148.086 150.302L145.674 147.485L143.423 150.432L145.808 152.253ZM85.9199 156.483L88.0262 154.347L86.0925 152.441L84.0034 154.175L85.9199 156.483ZM55 68.0107L54.9412 71.0102L58 71.0702V68.0107H55ZM110.145 30.9697L109.008 33.7461L111.486 34.7604L112.774 32.4137L110.145 30.9697ZM154 5V8C169.492 8 183.232 15.4926 191.799 27.0643L194.21 25.2793L196.621 23.4943C186.972 10.4601 171.474 2 154 2V5ZM194.21 25.2793L191.612 23.7794L184.268 36.5001L186.866 38L189.464 39.4999L196.808 26.7792L194.21 25.2793ZM186.866 38V35H164.413V38V41H186.866V38ZM164.413 38L161.815 36.5L150.268 56.5L152.866 58L155.464 59.5L167.011 39.5L164.413 38ZM152.866 58V55H130.413V58V61H152.866V58ZM130.413 58L127.815 56.5L116.845 75.5L119.443 77L122.041 78.5L133.011 59.5L130.413 58ZM119.443 77V74H96.4131V77V80H119.443V77ZM96.4131 77L93.815 75.5L81.4019 97L84 98.5L86.5981 100L99.0112 78.5L96.4131 77ZM84 98.5L81.4019 100L93.815 121.5L96.4131 120L99.0112 118.5L86.5981 97L84 98.5ZM96.4131 120V123H121.238V120V117H96.4131V120ZM121.238 120L123.836 121.5L134.806 102.5L132.208 101L129.61 99.5L118.64 118.5L121.238 120ZM132.208 101V104H153.443V101V98H132.208V101ZM153.443 101L150.845 102.5L161.815 121.5L164.413 120L167.011 118.5L156.041 99.5L153.443 101ZM164.413 120V123H187.443V120V117H164.413V120ZM187.443 120L184.845 121.5L195.815 140.5L198.413 139L201.011 137.5L190.041 118.5L187.443 120ZM198.413 139V142H223.238V139V136H198.413V139ZM223.238 139L225.836 140.5L237.383 120.5L234.785 119L232.187 117.5L220.64 137.5L223.238 139ZM234.785 119V122H256.238V119V116H234.785V119ZM256.238 119L258.836 120.5L270.383 100.5L267.785 99L265.187 97.5L253.64 117.5L256.238 119ZM267.785 99V102H281.26V99V96H267.785V99ZM281.26 99L278.485 100.141C280.748 105.644 282 111.672 282 118H285H288C288 110.874 286.589 104.072 284.034 97.8592L281.26 99ZM285 118H282C282 143.957 260.957 165 235 165V168V171C264.271 171 288 147.271 288 118H285ZM235 168V165C225.122 165 215.965 161.953 208.401 156.751L206.701 159.223L205.001 161.694C213.532 167.562 223.868 171 235 171V168ZM206.701 159.223L204.919 156.809C197.948 161.957 189.334 165 180 165V168V171C190.661 171 200.516 167.519 208.483 161.636L206.701 159.223ZM180 168V165C167.23 165 155.794 159.303 148.086 150.302L145.808 152.253L143.529 154.204C152.328 164.479 165.403 171 180 171V168ZM145.808 152.253L143.423 150.432C136.658 159.292 125.997 165 114 165V168V171C127.947 171 140.341 164.355 148.192 154.074L145.808 152.253ZM114 168V165C103.877 165 94.71 160.937 88.0262 154.347L85.9199 156.483L83.8137 158.62C91.5739 166.271 102.237 171 114 171V168ZM85.9199 156.483L84.0034 154.175C75.8617 160.936 65.4098 165 54 165V168V171C66.8619 171 78.6584 166.413 87.8364 158.791L85.9199 156.483ZM54 168V165C28.0426 165 7 143.957 7 118H4H1C1 147.271 24.7289 171 54 171V168ZM4 118H7C7 92.0426 28.0426 71 54 71V68V65C24.7289 65 1 88.7289 1 118H4ZM54 68V71C54.3093 71 54.6222 71.0039 54.9412 71.0102L55 68.0107L55.0588 65.0113C54.7127 65.0045 54.359 65 54 65V68ZM55 68.0107H58V68H55H52V68.0107H55ZM55 68H58C58 47.5655 74.5655 31 95 31V28V25C71.2518 25 52 44.2518 52 68H55ZM95 28V31C99.9625 31 104.69 31.9782 109.008 33.7461L110.145 30.9697L111.281 28.1934C106.256 26.1361 100.758 25 95 25V28ZM110.145 30.9697L112.774 32.4137C120.768 17.8567 136.234 8 154 8V5V2C133.957 2 116.519 13.1273 107.515 29.5258L110.145 30.9697Z" fill="url(#paint1_linear_64_4)" mask="url(#path-17-inside-1_64_4)"/>
</g>
<path d="M255.774 40.5L266.455 59L255.774 77.5H234.413L223.731 59L234.413 40.5H255.774Z" fill="#133644" stroke="#9AD3F1" stroke-width="3"/>
<g filter="url(#filter9_i_64_4)">
<path d="M155.641 100H132.547L121 80L132.547 60H155.641L167.188 80L155.641 100Z" fill="#9AD3F1"/>
</g>
<path d="M154.774 61.5L165.455 80L154.774 98.5H133.413L122.731 80L133.413 61.5H154.774Z" stroke="#133644" stroke-width="3"/>
<g filter="url(#filter10_i_64_4)">
<path d="M222.641 139H199.547L188 119L199.547 99H222.641L234.188 119L222.641 139Z" fill="#9AD3F1"/>
</g>
<path d="M221.774 100.5L232.455 119L221.774 137.5H200.413L189.731 119L200.413 100.5H221.774Z" stroke="#133644" stroke-width="3"/>
</g>
<path d="M49.1758 194.197C49.1758 191.053 49.9733 188.555 51.5684 186.705C53.1634 184.855 55.4648 183.93 58.4727 183.93C60.2044 183.93 61.6719 184.199 62.875 184.736C64.0781 185.274 64.9258 186.053 65.418 187.074C65.7461 187.749 65.6914 188.204 65.2539 188.441L63.6133 189.316C63.0391 189.626 62.5651 189.453 62.1914 188.797C61.9544 188.378 61.5124 187.999 60.8652 187.662C60.2272 187.325 59.4753 187.156 58.6094 187.156C56.6953 187.156 55.3555 187.762 54.5898 188.975C53.8242 190.178 53.4414 191.919 53.4414 194.197C53.4414 196.476 53.8242 198.221 54.5898 199.434C55.3555 200.637 56.6953 201.238 58.6094 201.238C59.6393 201.238 60.5052 201.006 61.207 200.541C61.918 200.076 62.4102 199.634 62.6836 199.215C63.1302 198.531 63.6042 198.358 64.1055 198.695L65.7461 199.789C66.1927 200.09 66.2383 200.546 65.8828 201.156C65.3542 202.068 64.4473 202.847 63.1621 203.494C61.877 204.141 60.3138 204.465 58.4727 204.465C55.5104 204.465 53.2181 203.54 51.5957 201.689C49.9824 199.839 49.1758 197.342 49.1758 194.197ZM70.5312 204C69.8932 204 69.5742 203.658 69.5742 202.975V185.42C69.5742 184.736 69.8932 184.395 70.5312 184.395H72.7461C73.293 184.395 73.5664 184.736 73.5664 185.42V200.609H81.5371C82.2207 200.609 82.5625 200.906 82.5625 201.498V203.111C82.5625 203.704 82.2207 204 81.5371 204H70.5312ZM84.4492 194.197C84.4492 191.053 85.2604 188.555 86.8828 186.705C88.5052 184.855 90.8203 183.93 93.8281 183.93C96.8359 183.93 99.151 184.855 100.773 186.705C102.396 188.555 103.207 191.053 103.207 194.197C103.207 197.342 102.396 199.839 100.773 201.689C99.151 203.54 96.8359 204.465 93.8281 204.465C90.8203 204.465 88.5052 203.54 86.8828 201.689C85.2604 199.839 84.4492 197.342 84.4492 194.197ZM88.7148 194.197C88.7148 196.476 89.1204 198.221 89.9316 199.434C90.752 200.637 92.0508 201.238 93.8281 201.238C95.6055 201.238 96.8997 200.637 97.7109 199.434C98.5312 198.221 98.9414 196.476 98.9414 194.197C98.9414 191.919 98.5312 190.178 97.7109 188.975C96.8997 187.762 95.6055 187.156 93.8281 187.156C92.0508 187.156 90.752 187.762 89.9316 188.975C89.1204 190.178 88.7148 191.919 88.7148 194.197ZM106.57 195.838V185.146C106.57 184.645 106.889 184.395 107.527 184.395H109.605C110.243 184.395 110.562 184.645 110.562 185.146V195.701C110.562 198.162 110.923 199.702 111.643 200.322C112.372 200.933 113.192 201.238 114.104 201.238C115.015 201.238 115.831 200.933 116.551 200.322C117.28 199.702 117.645 198.162 117.645 195.701V185.146C117.645 184.645 117.964 184.395 118.602 184.395H120.68C121.318 184.395 121.637 184.645 121.637 185.146V195.838C121.637 199.028 120.962 201.266 119.613 202.551C118.264 203.827 116.428 204.465 114.104 204.465C111.779 204.465 109.943 203.827 108.594 202.551C107.245 201.266 106.57 199.028 106.57 195.838ZM126.969 204C126.331 204 126.012 203.658 126.012 202.975V185.42C126.012 184.736 126.331 184.395 126.969 184.395H131.74C135.933 184.395 138.768 185.242 140.244 186.938C141.73 188.633 142.473 191.044 142.473 194.17C142.473 197.296 141.73 199.716 140.244 201.43C138.768 203.143 135.933 204 131.74 204H126.969ZM130.004 200.609H131.48C134.306 200.609 136.133 200.085 136.963 199.037C137.792 197.98 138.207 196.357 138.207 194.17C138.207 191.982 137.792 190.369 136.963 189.33C136.133 188.282 134.306 187.758 131.48 187.758H130.004V200.609ZM156.363 204C155.725 204 155.406 203.658 155.406 202.975V185.42C155.406 184.736 155.725 184.395 156.363 184.395H158.578C159.125 184.395 159.398 184.736 159.398 185.42V200.609H167.369C168.053 200.609 168.395 200.906 168.395 201.498V203.111C168.395 203.704 168.053 204 167.369 204H156.363ZM171.293 195.838V185.146C171.293 184.645 171.612 184.395 172.25 184.395H174.328C174.966 184.395 175.285 184.645 175.285 185.146V195.701C175.285 198.162 175.645 199.702 176.365 200.322C177.094 200.933 177.915 201.238 178.826 201.238C179.738 201.238 180.553 200.933 181.273 200.322C182.003 199.702 182.367 198.162 182.367 195.701V185.146C182.367 184.645 182.686 184.395 183.324 184.395H185.402C186.04 184.395 186.359 184.645 186.359 185.146V195.838C186.359 199.028 185.685 201.266 184.336 202.551C182.987 203.827 181.15 204.465 178.826 204.465C176.502 204.465 174.665 203.827 173.316 202.551C171.967 201.266 171.293 199.028 171.293 195.838ZM191.691 204C191.053 204 190.734 203.658 190.734 202.975V185.42C190.734 184.736 191.053 184.395 191.691 184.395H195.656C196.185 184.395 196.563 184.759 196.791 185.488L200.496 197.479C200.66 197.998 200.774 198.426 200.838 198.764C200.902 199.092 200.956 199.484 201.002 199.939H201.084C201.13 199.484 201.184 199.092 201.248 198.764C201.312 198.426 201.426 197.998 201.59 197.479L205.295 185.488C205.523 184.759 205.901 184.395 206.43 184.395H210.395C211.033 184.395 211.352 184.736 211.352 185.42V202.975C211.352 203.658 211.033 204 210.395 204H208.316C207.678 204 207.359 203.658 207.359 202.975V191.682C207.359 191.135 207.382 190.588 207.428 190.041H207.332L203.381 202.893C203.153 203.631 202.761 204 202.205 204H199.881C199.325 204 198.933 203.631 198.705 202.893L194.754 190.041H194.658C194.704 190.588 194.727 191.135 194.727 191.682V202.975C194.727 203.658 194.408 204 193.77 204H191.691ZM216.684 204C216.046 204 215.727 203.658 215.727 202.975V185.42C215.727 184.736 216.046 184.395 216.684 184.395H228.414C229.098 184.395 229.439 184.691 229.439 185.283V186.896C229.439 187.489 229.098 187.785 228.414 187.785H219.719V192.133H227.689C228.373 192.133 228.715 192.429 228.715 193.021V194.635C228.715 195.227 228.373 195.523 227.689 195.523H219.719V200.609H228.852C229.535 200.609 229.877 200.906 229.877 201.498V203.111C229.877 203.704 229.535 204 228.852 204H216.684ZM234.184 204C233.546 204 233.227 203.658 233.227 202.975V185.42C233.227 184.736 233.546 184.395 234.184 184.395H236.207C236.991 184.395 237.583 184.714 237.984 185.352L244.957 196.549H245.053C245.007 196.002 244.984 195.455 244.984 194.908V185.42C244.984 184.736 245.303 184.395 245.941 184.395H248.02C248.658 184.395 248.977 184.736 248.977 185.42V202.975C248.977 203.658 248.658 204 248.02 204H246.133C245.349 204 244.757 203.681 244.355 203.043L237.246 191.641H237.15C237.196 192.188 237.219 192.734 237.219 193.281V202.975C237.219 203.658 236.9 204 236.262 204H234.184Z" fill="url(#paint2_linear_64_4)"/>
<path d="M59.32 219V211.85H59.8V218.56H63.09V219H59.32ZM64.6325 219V211.85H67.9525V212.27H65.1125V214.9H67.6825V215.31H65.1125V218.58H68.0625V219H64.6325ZM70.3641 216.52L69.3341 219H68.8241L71.8341 211.8H72.1141L75.1341 219H74.5941L73.5641 216.52H70.3641ZM71.9441 212.6C71.9374 212.607 71.9241 212.647 71.9041 212.72C71.8107 213.007 71.7207 213.25 71.6341 213.45L70.5341 216.11H73.3941L72.2941 213.46C72.0607 212.953 71.9441 212.667 71.9441 212.6ZM78.9049 218.65C79.8449 218.65 80.5983 218.397 81.1649 217.89C81.7383 217.377 82.0249 216.607 82.0249 215.58C82.0249 214.553 81.7016 213.737 81.0549 213.13C80.4149 212.523 79.6216 212.22 78.6749 212.22C78.0683 212.22 77.5416 212.237 77.0949 212.27V218.57C77.5283 218.623 78.1316 218.65 78.9049 218.65ZM78.6749 211.78C79.7749 211.78 80.6949 212.123 81.4349 212.81C82.1749 213.49 82.5449 214.417 82.5449 215.59C82.5449 216.183 82.4483 216.71 82.2549 217.17C82.0683 217.623 81.8083 217.987 81.4749 218.26C80.7883 218.813 79.9316 219.09 78.9049 219.09C78.2316 219.09 77.4683 219.053 76.6149 218.98V211.85C77.2949 211.803 77.9816 211.78 78.6749 211.78ZM84.7692 219V211.85H85.2492V219H84.7692ZM88.3445 212.99L88.3645 213.84V219H87.8845V211.79H88.1045L92.8145 217.22C93.1345 217.6 93.3811 217.903 93.5545 218.13C93.5278 217.81 93.5145 217.443 93.5145 217.03V211.85H93.9945V219.06H93.7745L88.9745 213.53C88.6411 213.15 88.4278 212.897 88.3345 212.77L88.3445 212.99ZM99.7045 212.17C98.8311 212.17 98.1178 212.483 97.5645 213.11C97.0111 213.737 96.7345 214.507 96.7345 215.42C96.7345 216.327 97.0478 217.097 97.6745 217.73C98.3011 218.363 99.0645 218.68 99.9645 218.68C100.738 218.68 101.381 218.573 101.894 218.36V215.42H102.374V218.58C101.794 218.94 100.981 219.12 99.9345 219.12C98.8945 219.12 98.0145 218.763 97.2945 218.05C96.5745 217.33 96.2145 216.453 96.2145 215.42C96.2145 214.38 96.5411 213.507 97.1945 212.8C97.8478 212.087 98.6845 211.73 99.7045 211.73C100.504 211.73 101.314 211.863 102.134 212.13L102.004 212.53C101.204 212.29 100.438 212.17 99.7045 212.17ZM109.781 212.27H107.361V211.85H112.681V212.27H110.261V219H109.781V212.27ZM119.612 219V215.42H114.702V219H114.222V211.85H114.702V214.99H119.612V211.85H120.092V219H119.612ZM122.728 219V211.85H126.048V212.27H123.208V214.9H125.778V215.31H123.208V218.58H126.158V219H122.728ZM133.973 218.65C134.913 218.65 135.667 218.397 136.233 217.89C136.807 217.377 137.093 216.607 137.093 215.58C137.093 214.553 136.77 213.737 136.123 213.13C135.483 212.523 134.69 212.22 133.743 212.22C133.137 212.22 132.61 212.237 132.163 212.27V218.57C132.597 218.623 133.2 218.65 133.973 218.65ZM133.743 211.78C134.843 211.78 135.763 212.123 136.503 212.81C137.243 213.49 137.613 214.417 137.613 215.59C137.613 216.183 137.517 216.71 137.323 217.17C137.137 217.623 136.877 217.987 136.543 218.26C135.857 218.813 135 219.09 133.973 219.09C133.3 219.09 132.537 219.053 131.683 218.98V211.85C132.363 211.803 133.05 211.78 133.743 211.78ZM139.838 219V211.85H140.318V219H139.838ZM146.023 212.17C145.149 212.17 144.436 212.483 143.883 213.11C143.329 213.737 143.053 214.507 143.053 215.42C143.053 216.327 143.366 217.097 143.993 217.73C144.619 218.363 145.383 218.68 146.283 218.68C147.056 218.68 147.699 218.573 148.213 218.36V215.42H148.693V218.58C148.113 218.94 147.299 219.12 146.253 219.12C145.213 219.12 144.333 218.763 143.613 218.05C142.893 217.33 142.533 216.453 142.533 215.42C142.533 214.38 142.859 213.507 143.513 212.8C144.166 212.087 145.003 211.73 146.023 211.73C146.823 211.73 147.633 211.863 148.453 212.13L148.323 212.53C147.523 212.29 146.756 212.17 146.023 212.17ZM151.175 219V211.85H151.655V219H151.175ZM155.611 212.27H153.191V211.85H158.511V212.27H156.091V219H155.611V212.27ZM159.671 216.52L158.641 219H158.131L161.141 211.8H161.421L164.441 219H163.901L162.871 216.52H159.671ZM161.251 212.6C161.244 212.607 161.231 212.647 161.211 212.72C161.117 213.007 161.027 213.25 160.941 213.45L159.841 216.11H162.701L161.601 213.46C161.367 212.953 161.251 212.667 161.251 212.6ZM165.922 219V211.85H166.402V218.56H169.692V219H165.922ZM174.838 219V211.88C175.471 211.833 176.001 211.81 176.428 211.81C177.108 211.81 177.668 211.987 178.108 212.34C178.548 212.687 178.768 213.18 178.768 213.82C178.768 214.253 178.641 214.657 178.388 215.03C178.134 215.403 177.851 215.7 177.538 215.92C177.784 216.26 178.088 216.667 178.448 217.14C178.814 217.607 179.051 217.9 179.158 218.02C179.564 218.453 180.024 218.7 180.538 218.76L180.528 219.08C180.121 219.073 179.791 219.013 179.538 218.9C179.284 218.78 179.041 218.6 178.808 218.36C178.581 218.12 178.011 217.373 177.098 216.12C176.444 216.12 175.851 216.1 175.318 216.06V219H174.838ZM176.808 215.72C177.101 215.72 177.414 215.527 177.748 215.14C178.081 214.753 178.248 214.307 178.248 213.8C178.248 213.287 178.074 212.9 177.728 212.64C177.388 212.373 176.934 212.24 176.368 212.24C176.068 212.24 175.718 212.253 175.318 212.28V215.67C175.851 215.703 176.348 215.72 176.808 215.72ZM181.42 219V211.85H184.74V212.27H181.9V214.9H184.47V215.31H181.9V218.58H184.85V219H181.42ZM189.151 217.55L191.551 211.85H192.061L188.981 219.06H188.701L185.611 211.85H186.151L188.551 217.53C188.718 217.937 188.821 218.217 188.861 218.37C188.915 218.157 189.011 217.883 189.151 217.55ZM194.011 218.05C193.351 217.337 193.021 216.46 193.021 215.42C193.021 214.38 193.351 213.507 194.011 212.8C194.678 212.087 195.518 211.73 196.531 211.73C197.544 211.73 198.384 212.087 199.051 212.8C199.718 213.507 200.051 214.38 200.051 215.42C200.051 216.46 199.718 217.337 199.051 218.05C198.384 218.763 197.544 219.12 196.531 219.12C195.518 219.12 194.678 218.763 194.011 218.05ZM194.371 213.11C193.818 213.73 193.541 214.5 193.541 215.42C193.541 216.34 193.818 217.113 194.371 217.74C194.931 218.367 195.651 218.68 196.531 218.68C197.418 218.68 198.138 218.367 198.691 217.74C199.251 217.113 199.531 216.34 199.531 215.42C199.531 214.5 199.251 213.73 198.691 213.11C198.138 212.483 197.418 212.17 196.531 212.17C195.644 212.17 194.924 212.483 194.371 213.11ZM202.269 219V211.85H202.749V218.56H206.039V219H202.269ZM212.158 218.31C211.698 218.85 210.998 219.12 210.058 219.12C209.118 219.12 208.414 218.847 207.948 218.3C207.488 217.753 207.258 217.007 207.258 216.06V211.85H207.738V216.06C207.738 217.82 208.508 218.7 210.048 218.7C211.594 218.7 212.368 217.82 212.368 216.06V211.85H212.848V216.06C212.848 217.013 212.618 217.763 212.158 218.31ZM216.724 212.27H214.304V211.85H219.624V212.27H217.204V219H216.724V212.27ZM221.166 219V211.85H221.646V219H221.166ZM224.851 218.05C224.191 217.337 223.861 216.46 223.861 215.42C223.861 214.38 224.191 213.507 224.851 212.8C225.518 212.087 226.358 211.73 227.371 211.73C228.384 211.73 229.224 212.087 229.891 212.8C230.558 213.507 230.891 214.38 230.891 215.42C230.891 216.46 230.558 217.337 229.891 218.05C229.224 218.763 228.384 219.12 227.371 219.12C226.358 219.12 225.518 218.763 224.851 218.05ZM225.211 213.11C224.658 213.73 224.381 214.5 224.381 215.42C224.381 216.34 224.658 217.113 225.211 217.74C225.771 218.367 226.491 218.68 227.371 218.68C228.258 218.68 228.978 218.367 229.531 217.74C230.091 217.113 230.371 216.34 230.371 215.42C230.371 214.5 230.091 213.73 229.531 213.11C228.978 212.483 228.258 212.17 227.371 212.17C226.484 212.17 225.764 212.483 225.211 213.11ZM233.569 212.99L233.589 213.84V219H233.109V211.79H233.329L238.039 217.22C238.359 217.6 238.606 217.903 238.779 218.13C238.752 217.81 238.739 217.443 238.739 217.03V211.85H239.219V219.06H238.999L234.199 213.53C233.866 213.15 233.652 212.897 233.559 212.77L233.569 212.99Z" fill="url(#paint3_linear_64_4)"/>
<defs>
<filter id="filter0_d_64_4" x="82" y="79" width="54.1875" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_4" result="shape"/>
</filter>
<filter id="filter1_d_64_4" x="151" y="40" width="54.1875" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_4" result="shape"/>
</filter>
<filter id="filter2_d_64_4" x="184" y="20" width="54.1875" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_4" result="shape"/>
</filter>
<filter id="filter3_d_64_4" x="217" y="0" width="54.1875" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_4" result="shape"/>
</filter>
<filter id="filter4_d_64_4" x="151" y="79" width="54.1875" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_4" result="shape"/>
</filter>
<filter id="filter5_i_64_4" x="188" y="59" width="46.1875" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_64_4"/>
</filter>
<filter id="filter6_d_64_4" x="218" y="77" width="54.1875" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_4" result="shape"/>
</filter>
<filter id="filter7_i_64_4" x="256" y="59" width="46.1875" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_64_4"/>
</filter>
<filter id="filter8_d_64_4" x="0" y="5" width="289" height="171" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_4" result="shape"/>
</filter>
<filter id="filter9_i_64_4" x="121" y="60" width="46.1875" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_64_4"/>
</filter>
<filter id="filter10_i_64_4" x="188" y="99" width="46.1875" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_64_4"/>
</filter>
<linearGradient id="paint0_linear_64_4" x1="82" y1="39.5" x2="189" y2="168" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AD3F1"/>
<stop offset="1" stop-color="#089AD7"/>
</linearGradient>
<linearGradient id="paint1_linear_64_4" x1="144.5" y1="5" x2="144.5" y2="168" gradientUnits="userSpaceOnUse">
<stop stop-color="#089AD7"/>
<stop offset="1" stop-color="#9AD3F1"/>
</linearGradient>
<linearGradient id="paint2_linear_64_4" x1="150" y1="191" x2="150" y2="201" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AD3F1"/>
<stop offset="1" stop-color="#089AD7"/>
</linearGradient>
<linearGradient id="paint3_linear_64_4" x1="149.5" y1="211" x2="149.5" y2="221" gradientUnits="userSpaceOnUse">
<stop offset="0.5" stop-color="#9AD3F1"/>
<stop offset="1" stop-color="#089AD7"/>
</linearGradient>
<clipPath id="clip0_64_4">
<rect width="300" height="176" fill="white"/>
</clipPath>
</defs>
</svg>
