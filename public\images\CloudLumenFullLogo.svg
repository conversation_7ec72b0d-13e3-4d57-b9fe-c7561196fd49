<svg width="305" height="231" viewBox="0 0 305 231" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_64_4)">
<g filter="url(#filter0_d_64_4)">
<path d="M122.641 119H99.5469L88 99L99.5469 79H122.641L134.188 99L122.641 119Z" fill="#133644"/>
<path d="M121.774 80.5L132.455 99L121.774 117.5H100.413L89.7314 99L100.413 80.5H121.774Z" stroke="#9AD3F1" stroke-width="3"/>
</g>
<g filter="url(#filter1_d_64_4)">
<path d="M191.641 80H168.547L157 60L168.547 40H191.641L203.188 60L191.641 80Z" fill="#133644"/>
<path d="M190.774 41.5L201.455 60L190.774 78.5H169.413L158.731 60L169.413 41.5H190.774Z" stroke="#9AD3F1" stroke-width="3"/>
</g>
<g filter="url(#filter2_d_64_4)">
<path d="M224.641 60H201.547L190 40L201.547 20H224.641L236.188 40L224.641 60Z" fill="#133644"/>
<path d="M223.774 21.5L234.455 40L223.774 58.5H202.413L191.731 40L202.413 21.5H223.774Z" stroke="#9AD3F1" stroke-width="3"/>
</g>
<g filter="url(#filter3_d_64_4)">
<path d="M257.641 40H234.547L223 20L234.547 0H257.641L269.188 20L257.641 40Z" fill="#133644"/>
<path d="M256.774 1.5L267.455 20L256.774 38.5H235.413L224.731 20L235.413 1.5H256.774Z" stroke="#9AD3F1" stroke-width="3"/>
</g>
<g filter="url(#filter4_d_64_4)">
<path d="M191.641 119H168.547L157 99L168.547 79H191.641L203.188 99L191.641 119Z" fill="#133644"/>
<path d="M190.774 80.5L201.455 99L190.774 117.5H169.413L158.731 99L169.413 80.5H190.774Z" stroke="#9AD3F1" stroke-width="3"/>
</g>
<g filter="url(#filter5_i_64_4)">
<path d="M224.641 99H201.547L190 79L201.547 59H224.641L236.188 79L224.641 99Z" fill="#9AD3F1"/>
</g>
<path d="M223.774 60.5L234.455 79L223.774 97.5H202.413L191.731 79L202.413 60.5H223.774Z" stroke="#133644" stroke-width="3"/>
<g filter="url(#filter6_d_64_4)">
<path d="M258.641 117H235.547L224 97L235.547 77H258.641L270.188 97L258.641 117Z" fill="#133644"/>
<path d="M257.774 78.5L268.455 97L257.774 115.5H236.413L225.731 97L236.413 78.5H257.774Z" stroke="#9AD3F1" stroke-width="3"/>
</g>
<g filter="url(#filter7_i_64_4)">
<path d="M292.641 99H269.547L258 79L269.547 59H292.641L304.188 79L292.641 99Z" fill="#9AD3F1"/>
</g>
<path d="M291.774 60.5L302.455 79L291.774 97.5H270.413L259.731 79L270.413 60.5H291.774Z" stroke="#133644" stroke-width="3"/>
<g filter="url(#filter8_d_64_4)">
<mask id="path-17-inside-1_64_4" fill="white">
<path d="M156 5C172.483 5 187.102 12.9764 196.21 25.2793L188.866 38H166.413L154.866 58H132.413L121.443 77H98.4131L86 98.5L98.4131 120H123.238L134.208 101H155.443L166.413 120H189.443L200.413 139H225.238L236.785 119H258.238L269.785 99H283.26C285.668 104.858 287 111.273 287 118C287 145.614 264.614 168 237 168C226.495 168 216.748 164.757 208.701 159.223C201.232 164.738 191.997 168 182 168C168.316 168 156.061 161.891 147.808 152.253C140.499 161.823 128.972 168 116 168C105.057 168 95.1419 163.604 87.9199 156.483C79.2601 163.674 68.1358 168 56 168C28.3858 168 6 145.614 6 118C6 90.3858 28.3858 68 56 68C56.3341 68 56.6674 68.0042 57 68.0107V68C57 45.9086 74.9086 28 97 28C102.36 28 107.473 29.0572 112.145 30.9697C120.643 15.492 137.096 5 156 5Z"/>
</mask>
<path d="M156 5C172.483 5 187.102 12.9764 196.21 25.2793L188.866 38H166.413L154.866 58H132.413L121.443 77H98.4131L86 98.5L98.4131 120H123.238L134.208 101H155.443L166.413 120H189.443L200.413 139H225.238L236.785 119H258.238L269.785 99H283.26C285.668 104.858 287 111.273 287 118C287 145.614 264.614 168 237 168C226.495 168 216.748 164.757 208.701 159.223C201.232 164.738 191.997 168 182 168C168.316 168 156.061 161.891 147.808 152.253C140.499 161.823 128.972 168 116 168C105.057 168 95.1419 163.604 87.9199 156.483C79.2601 163.674 68.1358 168 56 168C28.3858 168 6 145.614 6 118C6 90.3858 28.3858 68 56 68C56.3341 68 56.6674 68.0042 57 68.0107V68C57 45.9086 74.9086 28 97 28C102.36 28 107.473 29.0572 112.145 30.9697C120.643 15.492 137.096 5 156 5Z" fill="url(#paint0_linear_64_4)"/>
<path d="M196.21 25.2793L198.808 26.7792L199.792 25.0754L198.621 23.4943L196.21 25.2793ZM188.866 38V41H190.598L191.464 39.4999L188.866 38ZM166.413 38V35H164.681L163.815 36.5L166.413 38ZM154.866 58V61H156.598L157.464 59.5L154.866 58ZM132.413 58V55H130.681L129.815 56.5L132.413 58ZM121.443 77V80H123.175L124.041 78.5L121.443 77ZM98.4131 77V74H96.681L95.815 75.5L98.4131 77ZM86 98.5L83.4019 97L82.5359 98.5L83.4019 100L86 98.5ZM98.4131 120L95.815 121.5L96.681 123H98.4131V120ZM123.238 120V123H124.97L125.836 121.5L123.238 120ZM134.208 101V98H132.476L131.61 99.5L134.208 101ZM155.443 101L158.041 99.5L157.175 98H155.443V101ZM166.413 120L163.815 121.5L164.681 123H166.413V120ZM189.443 120L192.041 118.5L191.175 117H189.443V120ZM200.413 139L197.815 140.5L198.681 142H200.413V139ZM225.238 139V142H226.97L227.836 140.5L225.238 139ZM236.785 119V116H235.053L234.187 117.5L236.785 119ZM258.238 119V122H259.97L260.836 120.5L258.238 119ZM269.785 99V96H268.053L267.187 97.5L269.785 99ZM283.26 99L286.034 97.8592L285.27 96H283.26V99ZM208.701 159.223L210.401 156.751L208.639 155.539L206.919 156.809L208.701 159.223ZM147.808 152.253L150.086 150.302L147.674 147.485L145.423 150.432L147.808 152.253ZM87.9199 156.483L90.0262 154.347L88.0925 152.441L86.0034 154.175L87.9199 156.483ZM57 68.0107L56.9412 71.0102L60 71.0702V68.0107H57ZM112.145 30.9697L111.008 33.7461L113.486 34.7604L114.774 32.4137L112.145 30.9697ZM156 5V8C171.492 8 185.232 15.4926 193.799 27.0643L196.21 25.2793L198.621 23.4943C188.972 10.4601 173.474 2 156 2V5ZM196.21 25.2793L193.612 23.7794L186.268 36.5001L188.866 38L191.464 39.4999L198.808 26.7792L196.21 25.2793ZM188.866 38V35H166.413V38V41H188.866V38ZM166.413 38L163.815 36.5L152.268 56.5L154.866 58L157.464 59.5L169.011 39.5L166.413 38ZM154.866 58V55H132.413V58V61H154.866V58ZM132.413 58L129.815 56.5L118.845 75.5L121.443 77L124.041 78.5L135.011 59.5L132.413 58ZM121.443 77V74H98.4131V77V80H121.443V77ZM98.4131 77L95.815 75.5L83.4019 97L86 98.5L88.5981 100L101.011 78.5L98.4131 77ZM86 98.5L83.4019 100L95.815 121.5L98.4131 120L101.011 118.5L88.5981 97L86 98.5ZM98.4131 120V123H123.238V120V117H98.4131V120ZM123.238 120L125.836 121.5L136.806 102.5L134.208 101L131.61 99.5L120.64 118.5L123.238 120ZM134.208 101V104H155.443V101V98H134.208V101ZM155.443 101L152.845 102.5L163.815 121.5L166.413 120L169.011 118.5L158.041 99.5L155.443 101ZM166.413 120V123H189.443V120V117H166.413V120ZM189.443 120L186.845 121.5L197.815 140.5L200.413 139L203.011 137.5L192.041 118.5L189.443 120ZM200.413 139V142H225.238V139V136H200.413V139ZM225.238 139L227.836 140.5L239.383 120.5L236.785 119L234.187 117.5L222.64 137.5L225.238 139ZM236.785 119V122H258.238V119V116H236.785V119ZM258.238 119L260.836 120.5L272.383 100.5L269.785 99L267.187 97.5L255.64 117.5L258.238 119ZM269.785 99V102H283.26V99V96H269.785V99ZM283.26 99L280.485 100.141C282.748 105.644 284 111.672 284 118H287H290C290 110.874 288.589 104.072 286.034 97.8592L283.26 99ZM287 118H284C284 143.957 262.957 165 237 165V168V171C266.271 171 290 147.271 290 118H287ZM237 168V165C227.122 165 217.965 161.953 210.401 156.751L208.701 159.223L207.001 161.694C215.532 167.562 225.868 171 237 171V168ZM208.701 159.223L206.919 156.809C199.948 161.957 191.334 165 182 165V168V171C192.661 171 202.516 167.519 210.483 161.636L208.701 159.223ZM182 168V165C169.23 165 157.794 159.303 150.086 150.302L147.808 152.253L145.529 154.204C154.328 164.479 167.403 171 182 171V168ZM147.808 152.253L145.423 150.432C138.658 159.292 127.997 165 116 165V168V171C129.947 171 142.341 164.355 150.192 154.074L147.808 152.253ZM116 168V165C105.877 165 96.71 160.937 90.0262 154.347L87.9199 156.483L85.8137 158.62C93.5739 166.271 104.237 171 116 171V168ZM87.9199 156.483L86.0034 154.175C77.8617 160.936 67.4098 165 56 165V168V171C68.8619 171 80.6584 166.413 89.8364 158.791L87.9199 156.483ZM56 168V165C30.0426 165 9 143.957 9 118H6H3C3 147.271 26.7289 171 56 171V168ZM6 118H9C9 92.0426 30.0426 71 56 71V68V65C26.7289 65 3 88.7289 3 118H6ZM56 68V71C56.3093 71 56.6222 71.0039 56.9412 71.0102L57 68.0107L57.0588 65.0113C56.7127 65.0045 56.359 65 56 65V68ZM57 68.0107H60V68H57H54V68.0107H57ZM57 68H60C60 47.5655 76.5655 31 97 31V28V25C73.2518 25 54 44.2518 54 68H57ZM97 28V31C101.963 31 106.69 31.9782 111.008 33.7461L112.145 30.9697L113.281 28.1934C108.256 26.1361 102.758 25 97 25V28ZM112.145 30.9697L114.774 32.4137C122.768 17.8567 138.234 8 156 8V5V2C135.957 2 118.519 13.1273 109.515 29.5258L112.145 30.9697Z" fill="url(#paint1_linear_64_4)" mask="url(#path-17-inside-1_64_4)"/>
</g>
<path d="M257.774 40.5L268.455 59L257.774 77.5H236.413L225.731 59L236.413 40.5H257.774Z" fill="#133644" stroke="#9AD3F1" stroke-width="3"/>
<g filter="url(#filter9_i_64_4)">
<path d="M157.641 100H134.547L123 80L134.547 60H157.641L169.188 80L157.641 100Z" fill="#9AD3F1"/>
</g>
<path d="M156.774 61.5L167.455 80L156.774 98.5H135.413L124.731 80L135.413 61.5H156.774Z" stroke="#133644" stroke-width="3"/>
<g filter="url(#filter10_i_64_4)">
<path d="M224.641 139H201.547L190 119L201.547 99H224.641L236.188 119L224.641 139Z" fill="#9AD3F1"/>
</g>
<path d="M223.774 100.5L234.455 119L223.774 137.5H202.413L191.731 119L202.413 100.5H223.774Z" stroke="#133644" stroke-width="3"/>
</g>
<path d="M1.76367 194.296C1.76367 189.579 2.95996 185.833 5.35254 183.058C7.74512 180.282 11.1973 178.895 15.709 178.895C18.3066 178.895 20.5078 179.298 22.3125 180.104C24.1172 180.911 25.3887 182.08 26.127 183.611C26.6191 184.623 26.5371 185.307 25.8809 185.662L23.4199 186.975C22.5586 187.439 21.8477 187.18 21.2871 186.195C20.9316 185.566 20.2686 184.999 19.2979 184.493C18.3408 183.987 17.2129 183.734 15.9141 183.734C13.043 183.734 11.0332 184.644 9.88477 186.462C8.73633 188.267 8.16211 190.878 8.16211 194.296C8.16211 197.714 8.73633 200.332 9.88477 202.15C11.0332 203.955 13.043 204.857 15.9141 204.857C17.459 204.857 18.7578 204.509 19.8105 203.812C20.877 203.114 21.6152 202.451 22.0254 201.822C22.6953 200.797 23.4062 200.537 24.1582 201.043L26.6191 202.684C27.2891 203.135 27.3574 203.818 26.8242 204.734C26.0312 206.102 24.6709 207.271 22.7432 208.241C20.8154 209.212 18.4707 209.697 15.709 209.697C11.2656 209.697 7.82715 208.31 5.39355 205.534C2.97363 202.759 1.76367 199.013 1.76367 194.296ZM33.7969 209C32.8398 209 32.3613 208.487 32.3613 207.462V181.13C32.3613 180.104 32.8398 179.592 33.7969 179.592H37.1191C37.9395 179.592 38.3496 180.104 38.3496 181.13V203.914H50.3057C51.3311 203.914 51.8438 204.358 51.8438 205.247V207.667C51.8438 208.556 51.3311 209 50.3057 209H33.7969ZM54.6738 194.296C54.6738 189.579 55.8906 185.833 58.3242 183.058C60.7578 180.282 64.2305 178.895 68.7422 178.895C73.2539 178.895 76.7266 180.282 79.1602 183.058C81.5938 185.833 82.8105 189.579 82.8105 194.296C82.8105 199.013 81.5938 202.759 79.1602 205.534C76.7266 208.31 73.2539 209.697 68.7422 209.697C64.2305 209.697 60.7578 208.31 58.3242 205.534C55.8906 202.759 54.6738 199.013 54.6738 194.296ZM61.0723 194.296C61.0723 197.714 61.6807 200.332 62.8975 202.15C64.1279 203.955 66.0762 204.857 68.7422 204.857C71.4082 204.857 73.3496 203.955 74.5664 202.15C75.7969 200.332 76.4121 197.714 76.4121 194.296C76.4121 190.878 75.7969 188.267 74.5664 186.462C73.3496 184.644 71.4082 183.734 68.7422 183.734C66.0762 183.734 64.1279 184.644 62.8975 186.462C61.6807 188.267 61.0723 190.878 61.0723 194.296ZM87.8555 196.757V180.72C87.8555 179.968 88.334 179.592 89.291 179.592H92.4082C93.3652 179.592 93.8438 179.968 93.8438 180.72V196.552C93.8438 200.243 94.3838 202.554 95.4639 203.483C96.5576 204.399 97.7881 204.857 99.1553 204.857C100.522 204.857 101.746 204.399 102.826 203.483C103.92 202.554 104.467 200.243 104.467 196.552V180.72C104.467 179.968 104.945 179.592 105.902 179.592H109.02C109.977 179.592 110.455 179.968 110.455 180.72V196.757C110.455 201.542 109.443 204.898 107.42 206.826C105.396 208.74 102.642 209.697 99.1553 209.697C95.6689 209.697 92.9141 208.74 90.8906 206.826C88.8672 204.898 87.8555 201.542 87.8555 196.757ZM118.453 209C117.496 209 117.018 208.487 117.018 207.462V181.13C117.018 180.104 117.496 179.592 118.453 179.592H125.61C131.899 179.592 136.151 180.863 138.366 183.406C140.595 185.949 141.709 189.565 141.709 194.255C141.709 198.944 140.595 202.574 138.366 205.145C136.151 207.715 131.899 209 125.61 209H118.453ZM123.006 203.914H125.221C129.459 203.914 132.2 203.128 133.444 201.556C134.688 199.97 135.311 197.536 135.311 194.255C135.311 190.974 134.688 188.554 133.444 186.995C132.2 185.423 129.459 184.637 125.221 184.637H123.006V203.914ZM162.545 209C161.588 209 161.109 208.487 161.109 207.462V181.13C161.109 180.104 161.588 179.592 162.545 179.592H165.867C166.688 179.592 167.098 180.104 167.098 181.13V203.914H179.054C180.079 203.914 180.592 204.358 180.592 205.247V207.667C180.592 208.556 180.079 209 179.054 209H162.545ZM184.939 196.757V180.72C184.939 179.968 185.418 179.592 186.375 179.592H189.492C190.449 179.592 190.928 179.968 190.928 180.72V196.552C190.928 200.243 191.468 202.554 192.548 203.483C193.642 204.399 194.872 204.857 196.239 204.857C197.606 204.857 198.83 204.399 199.91 203.483C201.004 202.554 201.551 200.243 201.551 196.552V180.72C201.551 179.968 202.029 179.592 202.986 179.592H206.104C207.061 179.592 207.539 179.968 207.539 180.72V196.757C207.539 201.542 206.527 204.898 204.504 206.826C202.48 208.74 199.726 209.697 196.239 209.697C192.753 209.697 189.998 208.74 187.975 206.826C185.951 204.898 184.939 201.542 184.939 196.757ZM215.537 209C214.58 209 214.102 208.487 214.102 207.462V181.13C214.102 180.104 214.58 179.592 215.537 179.592H221.484C222.277 179.592 222.845 180.139 223.187 181.232L228.744 199.218C228.99 199.997 229.161 200.64 229.257 201.146C229.353 201.638 229.435 202.226 229.503 202.909H229.626C229.694 202.226 229.776 201.638 229.872 201.146C229.968 200.64 230.139 199.997 230.385 199.218L235.942 181.232C236.284 180.139 236.852 179.592 237.645 179.592H243.592C244.549 179.592 245.027 180.104 245.027 181.13V207.462C245.027 208.487 244.549 209 243.592 209H240.475C239.518 209 239.039 208.487 239.039 207.462V190.522C239.039 189.702 239.073 188.882 239.142 188.062H238.998L233.071 207.339C232.729 208.446 232.142 209 231.308 209H227.821C226.987 209 226.399 208.446 226.058 207.339L220.131 188.062H219.987C220.056 188.882 220.09 189.702 220.09 190.522V207.462C220.09 208.487 219.611 209 218.654 209H215.537ZM253.025 209C252.068 209 251.59 208.487 251.59 207.462V181.13C251.59 180.104 252.068 179.592 253.025 179.592H270.621C271.646 179.592 272.159 180.036 272.159 180.925V183.345C272.159 184.233 271.646 184.678 270.621 184.678H257.578V191.199H269.534C270.56 191.199 271.072 191.644 271.072 192.532V194.952C271.072 195.841 270.56 196.285 269.534 196.285H257.578V203.914H271.277C272.303 203.914 272.815 204.358 272.815 205.247V207.667C272.815 208.556 272.303 209 271.277 209H253.025ZM279.275 209C278.318 209 277.84 208.487 277.84 207.462V181.13C277.84 180.104 278.318 179.592 279.275 179.592H282.311C283.486 179.592 284.375 180.07 284.977 181.027L295.436 197.823H295.579C295.511 197.003 295.477 196.183 295.477 195.362V181.13C295.477 180.104 295.955 179.592 296.912 179.592H300.029C300.986 179.592 301.465 180.104 301.465 181.13V207.462C301.465 208.487 300.986 209 300.029 209H297.199C296.023 209 295.135 208.521 294.533 207.564L283.869 190.461H283.726C283.794 191.281 283.828 192.102 283.828 192.922V207.462C283.828 208.487 283.35 209 282.393 209H279.275Z" fill="url(#paint2_linear_64_4)"/>
<path d="M12.112 230V218.56H12.88V229.296H18.144V230H12.112ZM20.612 230V218.56H25.924V219.232H21.38V223.44H25.492V224.096H21.38V229.328H26.1V230H20.612ZM29.7825 226.032L28.1345 230H27.3185L32.1345 218.48H32.5825L37.4145 230H36.5505L34.9025 226.032H29.7825ZM32.3105 219.76C32.2998 219.771 32.2785 219.835 32.2465 219.952C32.0972 220.411 31.9532 220.8 31.8145 221.12L30.0545 225.376H34.6305L32.8705 221.136C32.4972 220.325 32.3105 219.867 32.3105 219.76ZM43.4479 229.44C44.9519 229.44 46.1572 229.035 47.0639 228.224C47.9812 227.403 48.4399 226.171 48.4399 224.528C48.4399 222.885 47.9225 221.579 46.8879 220.608C45.8639 219.637 44.5945 219.152 43.0799 219.152C42.1092 219.152 41.2665 219.179 40.5519 219.232V229.312C41.2452 229.397 42.2105 229.44 43.4479 229.44ZM43.0799 218.448C44.8399 218.448 46.3119 218.997 47.4959 220.096C48.6799 221.184 49.2719 222.667 49.2719 224.544C49.2719 225.493 49.1172 226.336 48.8079 227.072C48.5092 227.797 48.0932 228.379 47.5599 228.816C46.4612 229.701 45.0905 230.144 43.4479 230.144C42.3705 230.144 41.1492 230.085 39.7839 229.968V218.56C40.8719 218.485 41.9705 218.448 43.0799 218.448ZM52.8307 230V218.56H53.5988V230H52.8307ZM58.5511 220.384L58.5831 221.744V230H57.8151V218.464H58.1671L65.7031 227.152C66.2151 227.76 66.6098 228.245 66.8871 228.608C66.8445 228.096 66.8231 227.509 66.8231 226.848V218.56H67.5911V230.096H67.2391L59.5591 221.248C59.0258 220.64 58.6845 220.235 58.5351 220.032L58.5511 220.384ZM76.7271 219.072C75.3298 219.072 74.1885 219.573 73.3031 220.576C72.4178 221.579 71.9751 222.811 71.9751 224.272C71.9751 225.723 72.4765 226.955 73.4791 227.968C74.4818 228.981 75.7031 229.488 77.1431 229.488C78.3805 229.488 79.4098 229.317 80.2311 228.976V224.272H80.9991V229.328C80.0711 229.904 78.7698 230.192 77.0951 230.192C75.4311 230.192 74.0231 229.621 72.8711 228.48C71.7191 227.328 71.1431 225.925 71.1431 224.272C71.1431 222.608 71.6658 221.211 72.7111 220.08C73.7565 218.939 75.0951 218.368 76.7271 218.368C78.0071 218.368 79.3031 218.581 80.6151 219.008L80.4071 219.648C79.1271 219.264 77.9005 219.072 76.7271 219.072ZM92.849 219.232H88.977V218.56H97.489V219.232H93.617V230H92.849V219.232ZM108.58 230V224.272H100.724V230H99.9557V218.56H100.724V223.584H108.58V218.56H109.348V230H108.58ZM113.565 230V218.56H118.877V219.232H114.333V223.44H118.445V224.096H114.333V229.328H119.053V230H113.565ZM131.557 229.44C133.061 229.44 134.267 229.035 135.173 228.224C136.091 227.403 136.549 226.171 136.549 224.528C136.549 222.885 136.032 221.579 134.997 220.608C133.973 219.637 132.704 219.152 131.189 219.152C130.219 219.152 129.376 219.179 128.661 219.232V229.312C129.355 229.397 130.32 229.44 131.557 229.44ZM131.189 218.448C132.949 218.448 134.421 218.997 135.605 220.096C136.789 221.184 137.381 222.667 137.381 224.544C137.381 225.493 137.227 226.336 136.917 227.072C136.619 227.797 136.203 228.379 135.669 228.816C134.571 229.701 133.2 230.144 131.557 230.144C130.48 230.144 129.259 230.085 127.893 229.968V218.56C128.981 218.485 130.08 218.448 131.189 218.448ZM140.94 230V218.56H141.708V230H140.94ZM150.837 219.072C149.439 219.072 148.298 219.573 147.413 220.576C146.527 221.579 146.085 222.811 146.085 224.272C146.085 225.723 146.586 226.955 147.589 227.968C148.591 228.981 149.813 229.488 151.253 229.488C152.49 229.488 153.519 229.317 154.341 228.976V224.272H155.109V229.328C154.181 229.904 152.879 230.192 151.205 230.192C149.541 230.192 148.133 229.621 146.981 228.48C145.829 227.328 145.253 225.925 145.253 224.272C145.253 222.608 145.775 221.211 146.821 220.08C147.866 218.939 149.205 218.368 150.837 218.368C152.117 218.368 153.413 218.581 154.725 219.008L154.517 219.648C153.237 219.264 152.01 219.072 150.837 219.072ZM159.081 230V218.56H159.849V230H159.081ZM166.177 219.232H162.305V218.56H170.817V219.232H166.945V230H166.177V219.232ZM172.673 226.032L171.025 230H170.209L175.025 218.48H175.473L180.305 230H179.441L177.793 226.032H172.673ZM175.201 219.76C175.19 219.771 175.169 219.835 175.137 219.952C174.988 220.411 174.844 220.8 174.705 221.12L172.945 225.376H177.521L175.761 221.136C175.388 220.325 175.201 219.867 175.201 219.76ZM182.674 230V218.56H183.443V229.296H188.707V230H182.674ZM196.94 230V218.608C197.953 218.533 198.801 218.496 199.484 218.496C200.572 218.496 201.468 218.779 202.172 219.344C202.876 219.899 203.228 220.688 203.228 221.712C203.228 222.405 203.025 223.051 202.62 223.648C202.215 224.245 201.761 224.72 201.26 225.072C201.655 225.616 202.14 226.267 202.716 227.024C203.303 227.771 203.681 228.24 203.852 228.432C204.503 229.125 205.239 229.52 206.06 229.616L206.044 230.128C205.393 230.117 204.865 230.021 204.46 229.84C204.055 229.648 203.665 229.36 203.292 228.976C202.929 228.592 202.017 227.397 200.556 225.392C199.511 225.392 198.561 225.36 197.708 225.296V230H196.94ZM200.092 224.752C200.561 224.752 201.063 224.443 201.596 223.824C202.129 223.205 202.396 222.491 202.396 221.68C202.396 220.859 202.119 220.24 201.564 219.824C201.02 219.397 200.295 219.184 199.388 219.184C198.908 219.184 198.348 219.205 197.708 219.248V224.672C198.561 224.725 199.356 224.752 200.092 224.752ZM207.471 230V218.56H212.783V219.232H208.239V223.44H212.351V224.096H208.239V229.328H212.959V230H207.471ZM219.842 227.68L223.682 218.56H224.498L219.57 230.096H219.122L214.178 218.56H215.042L218.882 227.648C219.149 228.299 219.314 228.747 219.378 228.992C219.463 228.651 219.618 228.213 219.842 227.68ZM227.618 228.48C226.562 227.339 226.034 225.936 226.034 224.272C226.034 222.608 226.562 221.211 227.618 220.08C228.684 218.939 230.028 218.368 231.65 218.368C233.271 218.368 234.615 218.939 235.682 220.08C236.748 221.211 237.282 222.608 237.282 224.272C237.282 225.936 236.748 227.339 235.682 228.48C234.615 229.621 233.271 230.192 231.65 230.192C230.028 230.192 228.684 229.621 227.618 228.48ZM228.194 220.576C227.308 221.568 226.866 222.8 226.866 224.272C226.866 225.744 227.308 226.981 228.194 227.984C229.09 228.987 230.242 229.488 231.65 229.488C233.068 229.488 234.22 228.987 235.106 227.984C236.002 226.981 236.45 225.744 236.45 224.272C236.45 222.8 236.002 221.568 235.106 220.576C234.22 219.573 233.068 219.072 231.65 219.072C230.231 219.072 229.079 219.573 228.194 220.576ZM240.831 230V218.56H241.599V229.296H246.863V230H240.831ZM256.652 228.896C255.916 229.76 254.796 230.192 253.292 230.192C251.788 230.192 250.663 229.755 249.916 228.88C249.18 228.005 248.812 226.811 248.812 225.296V218.56H249.58V225.296C249.58 228.112 250.812 229.52 253.276 229.52C255.751 229.52 256.988 228.112 256.988 225.296V218.56H257.756V225.296C257.756 226.821 257.388 228.021 256.652 228.896ZM263.958 219.232H260.086V218.56H268.598V219.232H264.726V230H263.958V219.232ZM271.065 230V218.56H271.833V230H271.065ZM276.962 228.48C275.906 227.339 275.378 225.936 275.378 224.272C275.378 222.608 275.906 221.211 276.962 220.08C278.028 218.939 279.372 218.368 280.994 218.368C282.615 218.368 283.959 218.939 285.026 220.08C286.092 221.211 286.626 222.608 286.626 224.272C286.626 225.936 286.092 227.339 285.026 228.48C283.959 229.621 282.615 230.192 280.994 230.192C279.372 230.192 278.028 229.621 276.962 228.48ZM277.538 220.576C276.652 221.568 276.21 222.8 276.21 224.272C276.21 225.744 276.652 226.981 277.538 227.984C278.434 228.987 279.586 229.488 280.994 229.488C282.412 229.488 283.564 228.987 284.45 227.984C285.346 226.981 285.794 225.744 285.794 224.272C285.794 222.8 285.346 221.568 284.45 220.576C283.564 219.573 282.412 219.072 280.994 219.072C279.575 219.072 278.423 219.573 277.538 220.576ZM290.911 220.384L290.943 221.744V230H290.174V218.464H290.527L298.062 227.152C298.575 227.76 298.969 228.245 299.247 228.608C299.204 228.096 299.183 227.509 299.183 226.848V218.56H299.951V230.096H299.599L291.919 221.248C291.385 220.64 291.044 220.235 290.895 220.032L290.911 220.384Z" fill="url(#paint3_linear_64_4)"/>
<defs>
<filter id="filter0_d_64_4" x="84" y="79" width="54.1875" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_4" result="shape"/>
</filter>
<filter id="filter1_d_64_4" x="153" y="40" width="54.1875" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_4" result="shape"/>
</filter>
<filter id="filter2_d_64_4" x="186" y="20" width="54.1875" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_4" result="shape"/>
</filter>
<filter id="filter3_d_64_4" x="219" y="0" width="54.1875" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_4" result="shape"/>
</filter>
<filter id="filter4_d_64_4" x="153" y="79" width="54.1875" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_4" result="shape"/>
</filter>
<filter id="filter5_i_64_4" x="190" y="59" width="46.1875" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_64_4"/>
</filter>
<filter id="filter6_d_64_4" x="220" y="77" width="54.1875" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_4" result="shape"/>
</filter>
<filter id="filter7_i_64_4" x="258" y="59" width="46.1875" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_64_4"/>
</filter>
<filter id="filter8_d_64_4" x="2" y="5" width="289" height="171" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_4" result="shape"/>
</filter>
<filter id="filter9_i_64_4" x="123" y="60" width="46.1875" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_64_4"/>
</filter>
<filter id="filter10_i_64_4" x="190" y="99" width="46.1875" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_64_4"/>
</filter>
<linearGradient id="paint0_linear_64_4" x1="84" y1="39.5" x2="191" y2="168" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AD3F1"/>
<stop offset="1" stop-color="#089AD7"/>
</linearGradient>
<linearGradient id="paint1_linear_64_4" x1="146.5" y1="5" x2="146.5" y2="168" gradientUnits="userSpaceOnUse">
<stop stop-color="#089AD7"/>
<stop offset="1" stop-color="#9AD3F1"/>
</linearGradient>
<linearGradient id="paint2_linear_64_4" x1="152.5" y1="180" x2="152.5" y2="209" gradientUnits="userSpaceOnUse">
<stop stop-color="#9AD3F1"/>
<stop offset="1" stop-color="#089AD7"/>
</linearGradient>
<linearGradient id="paint3_linear_64_4" x1="156.5" y1="219" x2="156.5" y2="230" gradientUnits="userSpaceOnUse">
<stop offset="0.5" stop-color="#9AD3F1"/>
<stop offset="1" stop-color="#089AD7"/>
</linearGradient>
<clipPath id="clip0_64_4">
<rect width="300" height="176" fill="white" transform="translate(2)"/>
</clipPath>
</defs>
</svg>
